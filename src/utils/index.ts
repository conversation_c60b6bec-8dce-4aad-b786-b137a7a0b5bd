// Re-export all utility modules
export { default as cleanData } from "./cleanData";
export { type AppConfigs, default as getConfig, getConfigs } from "./configs";
export { default as createError } from "./createError";
	/** Whether to remove empty strings (default: true) */
	removeEmptyStrings?: boolean;
	/** Whether to remove empty arrays (default: true) */
	removeEmptyArrays?: boolean;
	/** Whether to remove empty objects (default: true) */
	removeEmptyObjects?: boolean;
	/** Whether to trim string values (default: true) */
	trimStrings?: boolean;
	/** Custom predicate to determine if a value should be kept */
	keepValue?: (value: unknown, key?: string | number) => boolean;
}

/**
 * Checks if a value is considered "empty" based on its type
 * @param value - Value to check
 * @param options - Cleaning options
 * @returns True if the value should be considered empty
 */
const isEmpty = (
	value: unknown,
	options: Required<CleanDataOptions>,
): boolean => {
	if (value === null || value === undefined) return true;

	if (typeof value === "string") {
		const stringValue = options.trimStrings ? value.trim() : value;
		return options.removeEmptyStrings && stringValue === "";
	}

	if (Array.isArray(value)) {
		return options.removeEmptyArrays && value.length === 0;
	}

	if (typeof value === "object" && value !== null) {
		return options.removeEmptyObjects && Object.keys(value).length === 0;
	}

	return false;
};

/**
 * Recursively removes null, undefined, empty strings, empty arrays, and empty objects from arrays and objects.
 * Enhanced with configurable options and better type safety.
 *
 * @param data - The data to clean (can be any type)
 * @param options - Configuration options for cleaning behavior
 * @returns Cleaned data with null/undefined/empty values removed
 *
 * @example
 * ```typescript
* // Clean nested object with default options
 * const dirtyObj = {
 *   name: "John",
 *   age: null,
 *   email: "",
 *   address: {
 *     street: "123 Main St",
 *     city: null,
 *     zip: undefined
 *   },
 *   hobbies: ["reading", null, "", undefined, "gaming"]
 * };
 *
 * const cleanObj = cleanData(dirtyObj);
 * // Result: {
 * //   name: "John",
 * //   address: { street: "123 Main St" },
 * //   hobbies: ["reading", "gaming"]
 * // }
 *
 * // Clean with custom options
 * const partialClean = cleanData(dirtyObj, {
 *   removeEmptyStrings: false,
 *   keepValue: (value, key) => key === "email" // Always keep email field
 * });
 *
```
 */
const cleanData = <T>(data: T, options: CleanDataOptions = {}): T => {
	// Set default options
	const defaultOptions: Required<CleanDataOptions> = {
		removeEmptyStrings: true,
		removeEmptyArrays: true,
		removeEmptyObjects: true,
		trimStrings: true,
		keepValue: () => false,
		...options,
	};

	// Handle null/undefined values
	if (data === null || data === undefined) {
		return data;
	}

	// Handle arrays
	if (Array.isArray(data)) {
		const cleanedArray = data
			.map((item) => cleanData(item, options))
			.filter((item, index) => {
				// Use custom predicate if provided
				if (defaultOptions.keepValue(item, index)) {
					return true;
				}
				// Otherwise use standard empty check
				return !isEmpty(item, defaultOptions);
			});

		return cleanedArray as T;
	}

	// Handle objects (but not Date, RegExp, etc.)
	if (
		typeof data === "object" &&
		data !== null &&
		data.constructor === Object
	) {
		const cleanedObject: Record<string, unknown> = {};

		for (const [key, value] of Object.entries(data)) {
			const cleanedValue = cleanData(value, options);

			// Use custom predicate if provided
			if (defaultOptions.keepValue(cleanedValue, key)) {
				cleanedObject[key] = cleanedValue;
				continue;
			}

			// Skip empty values based on options
			if (!isEmpty(cleanedValue, defaultOptions)) {
				// Apply string trimming if enabled
				if (typeof cleanedValue === "string" && defaultOptions.trimStrings) {
					cleanedObject[key] = cleanedValue.trim();
				} else {
					cleanedObject[key] = cleanedValue;
				}
			}
		}

		return cleanedObject as T;
	}

	// Handle primitive values
	if (typeof data === "string" && defaultOptions.trimStrings) {
		return data.trim() as T;
	}

	// Return other values as-is (numbers, booleans, functions, Date objects, etc.)
	return data;
