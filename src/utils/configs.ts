import { HTTPException } from "hono/http-exception";
import type { ContentfulStatusCode } from "hono/utils/http-status";

/**
 * Enhanced error creation options
 */
export interface ErrorOptions {
	/** Error message */
	message: string;
	/** HTTP status code */
	status: ContentfulStatusCode;
	/** Error cause (original error) */
	cause?: unknown;
	/** Response object */
	res?: Response;
	/** Additional error context */
	context?: Record<string, unknown>;
	/** Error code for categorization */
	code?: string;
}

/**
 * Common HTTP status codes for convenience
 */
export const HTTP_STATUS = {
	BAD_REQUEST: 400,
	UNAUTHORIZED: 401,
	FORBIDDEN: 403,
	NOT_FOUND: 404,
	CONFLICT: 409,
	UNPROCESSABLE_ENTITY: 422,
	TOO_MANY_REQUESTS: 429,
	INTERNAL_SERVER_ERROR: 500,
	BAD_GATEWAY: 502,
	SERVICE_UNAVAILABLE: 503,
	GATEWAY_TIMEOUT: 504,
} as const;

/**
 * Enhanced error creation utility with better type safety and context support
 *
 * @param messageOrOptions - Error message string or detailed options object
 * @param status - HTTP status code (used only when first param is string)
 * @returns HTTPException instance with enhanced error information
 *
 * @example
 * ```typescript
* // Simple error
 * throw createError("User not found", 404);
 *
 * // Detailed error with context
 * throw createError({
 *   message: "Failed to sync patient data",
 *   status: 422,
 *   code: "SYNC_ERROR",
 *   context: { patientId: 123, platform: "CC" },
 *   cause: originalError
 * });
 *
```
 */
const createError = (
	messageOrOptions: string | ErrorOptions,
	status: ContentfulStatusCode = 500,
): HTTPException => {
	if (typeof messageOrOptions === "string") {
		return new HTTPException(status, {
			message: messageOrOptions,
			// Add timestamp for better debugging
			cause: {
				timestamp: new Date().toISOString(),
				status,
			}
		});
	}

	const {
		message,
		cause,
		res,
		status: statusCode,
		context,
		code
	} = messageOrOptions;

	// Enhanced cause object with additional context
	const enhancedCause = {
		timestamp: new Date().toISOString(),
		status: statusCode || status,
		code,
		context,
		originalCause: cause,
	};

	return new HTTPException(statusCode || status, {
		message,
		cause: enhancedCause,
		res,
	});
};

/**
 * Create a validation error (400 Bad Request)
 * @param message - Error message
 * @param context - Additional validation context
 * @returns HTTPException for validation errors
 */
export const createValidationError = (
	message: string,
	context?: Record<string, unknown>,
): HTTPException => {
	return createError({
		message,
		status: HTTP_STATUS.BAD_REQUEST,
		code: "VALIDATION_ERROR",
		context,
	});
};

/**
 * Create an authentication error (401 Unauthorized)
 * @param message - Error message
 * @param context - Additional auth context
 * @returns HTTPException for authentication errors
 */
export const createAuthError = (
	message: string = "Authentication required",
	context?: Record<string, unknown>,
): HTTPException => {
	return createError({
		message,
		status: HTTP_STATUS.UNAUTHORIZED,
		code: "AUTH_ERROR",
		context,
	});
};

/**
 * Create a not found error (404 Not Found)
 * @param resource - Resource that was not found
 * @param context - Additional context
 * @returns HTTPException for not found errors
 */
export const createNotFoundError = (
	resource: string,
	context?: Record<string, unknown>,
): HTTPException => {
	return createError({
		message: `${resource} not found`,
		status: HTTP_STATUS.NOT_FOUND,
		code: "NOT_FOUND",
		context,
	});
};

/**
 * Create a sync error (422 Unprocessable Entity)
 * @param message - Error message
 * @param platform - Platform where sync failed
 * @param context - Additional sync context
 * @returns HTTPException for sync errors
 */
export const createSyncError = (
	message: string,
	platform: "CC" | "AP",
	context?: Record<string, unknown>,
): HTTPException => {
	return createError({
		message,
		status: HTTP_STATUS.UNPROCESSABLE_ENTITY,
		code: "SYNC_ERROR",
		context: {
			platform,
			...context,
		},
	});
};

export default createError;


/**
 * Environment variable mapping for configuration keys
 */
const ENV_MAPPING: Record<keyof AppConfigs, string> = {
  databaseUrl: "DATABASE_URL",
  ccApiDomain: "CC_API_DOMAIN",
  ccApiKey: "CC_API_KEY",
  apApiKey: "AP_API_KEY",
  apApiDomain: "AP_API_DOMAIN",
  cacheTTL: "CACHE_TTL",
  maxRetries: "MAX_RETRIES",
  requestTimeout: "REQUEST_TIMEOUT",
  locationID: "LOCATION_ID",
  syncBufferTimeSec: "SYNC_BUFFER_TIME_SEC",
  apCalendarId: "AP_CALENDAR_ID",
};

/**
 * Default configuration values
 * In production, these should be overridden by environment variables
 */
const DEFAULT_CONFIGS: AppConfigs = {
  databaseUrl:
    "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require",
  ccApiDomain: "https://ccdemo.clinicore.eu/api/v1",
  ccApiKey:
    "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  apApiDomain: "https://services.leadconnectorhq.com",
  apApiKey: "pit-19f7d314-acb6-45d7-bae7-2a56ccf789b6",
  cacheTTL: 300, // 5 minutes
  maxRetries: 3,
  requestTimeout: 30000, // 30 seconds
  locationID: "CIY0QcIvP7m9TxVWlvy3",
  syncBufferTimeSec: 60, // 1 minute buffer time to prevent unnecessary syncs
  apCalendarId: "ZsyisrZNAGBZsLAE60zj",
};

/**
 * Parse environment variable value to appropriate type
 * @param value - String value from environment
 * @param defaultValue - Default value to determine type
 * @returns Parsed value with correct type
 */
const parseEnvValue = <T>(value: string | undefined, defaultValue: T): T => {
  if (value === undefined) {
    return defaultValue;
  }

  // Handle different types based on default value
  if (typeof defaultValue === "number") {
    const parsed = Number(value);
    return (Number.isNaN(parsed) ? defaultValue : parsed) as T;
  }

  if (typeof defaultValue === "boolean") {
    return (value.toLowerCase() === "true") as T;
  }

  // Return as string for all other types
  return value as T;
};

/**
 * Load configuration from environment variables with fallback to defaults
 * @returns Complete configuration object
 */
const loadConfigs = (): AppConfigs => {
  // Start with default configs and override with environment variables
  return {
    databaseUrl: parseEnvValue(
      process?.env?.DATABASE_URL,
      DEFAULT_CONFIGS.databaseUrl
    ),
    ccApiDomain: parseEnvValue(
      process?.env?.CC_API_DOMAIN,
      DEFAULT_CONFIGS.ccApiDomain
    ),
    ccApiKey: parseEnvValue(process?.env?.CC_API_KEY, DEFAULT_CONFIGS.ccApiKey),
    apApiKey: parseEnvValue(process?.env?.AP_API_KEY, DEFAULT_CONFIGS.apApiKey),
    apApiDomain: parseEnvValue(
      process?.env?.AP_API_DOMAIN,
      DEFAULT_CONFIGS.apApiDomain
    ),
    cacheTTL: parseEnvValue(process?.env?.CACHE_TTL, DEFAULT_CONFIGS.cacheTTL),
    maxRetries: parseEnvValue(
      process?.env?.MAX_RETRIES,
      DEFAULT_CONFIGS.maxRetries
    ),
    requestTimeout: parseEnvValue(
      process?.env?.REQUEST_TIMEOUT,
      DEFAULT_CONFIGS.requestTimeout
    ),
    locationID: parseEnvValue(
      process?.env?.LOCATION_ID,
      DEFAULT_CONFIGS.locationID
    ),
    syncBufferTimeSec: parseEnvValue(
      process?.env?.SYNC_BUFFER_TIME_SEC,
      DEFAULT_CONFIGS.syncBufferTimeSec
    ),
    apCalendarId: parseEnvValue(
      process?.env?.AP_CALENDAR_ID,
      DEFAULT_CONFIGS.apCalendarId
    ),
  };
};

// Initialize configuration
const configs = loadConfigs();

/**
 * Get a specific configuration value with enhanced error handling
 * @param key - Configuration key to retrieve
 * @returns The configuration value
 * @throws Error if configuration value is missing or invalid
 */
export const getConfig = <K extends keyof AppConfigs>(
  key: K
): AppConfigs[K] => {
  const value = configs[key];

  if (value === undefined || value === null) {
    const envKey = ENV_MAPPING[key];
    console.error(
      `Config '${key}' is not defined. Set environment variable '${envKey}' or check default configuration.`
    );
    throw new Error(
      `Config '${key}' is not defined. Set environment variable '${envKey}' or check default configuration.`
    );
  }

  // Additional validation for critical configs
  if (typeof value === "string" && value.trim() === "") {
    console.error(`Config '${key}' is empty.`);
    throw new Error(`Config '${key}' is empty.`);
  }

  return value;
};

/**
 * Get all configuration values
 * @returns Complete configuration object (readonly)
 */
export const getConfigs = (): Readonly<AppConfigs> => {
  return Object.freeze({ ...configs });
};

/**
 * Validate all required configurations are present
 * @throws Error if any required configuration is missing
 */
export const validateConfigs = (): void => {
  const requiredKeys: (keyof AppConfigs)[] = [
    "databaseUrl",
    "ccApiDomain",
    "ccApiKey",
    "apApiKey",
    "apApiDomain",
    "locationID",
    "apCalendarId",
  ];

  for (const key of requiredKeys) {
    try {
      getConfig(key);
    } catch (error) {
      throw new Error(
        `Configuration validation failed: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }
};

export default getConfig;
