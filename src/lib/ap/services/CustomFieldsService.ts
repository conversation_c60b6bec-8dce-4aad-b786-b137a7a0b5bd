import { APCustomFieldRequest } from "@libAP/requests";
// import { CCPatientRequest } from "@libCC/request/patientRequest";

export class CustomFieldsService {
  private request: APCustomFieldRequest;
  private LocalPatient: TLocalPatient;
  // private patientRequest: CCPatientRequest;

  constructor(patient: TLocalPatient) {
    this.request = new APCustomFieldRequest();
    // this.patientRequest = new CCPatientRequest();
    this.LocalPatient = patient;
  }

  async syncAllCustomFields() {
    if (!this.LocalPatient.ccData?.customFields?.length) {
      console.log("Custom fields not found for patient, skipping sync.");
      return;
    }
    await this.appointmentCustomFields();
    await this.invoiceCustomFields();
    await this.paymentCustomFields();
  }

  async appointmentCustomFields() {}

  async invoiceCustomFields() {}

  async paymentCustomFields() {}

  async getAllCustomFields() {
    return await this.request.all();
  }

  async getNameValue(): Promise<IKeyValue[]> {
    if (!this.LocalPatient.apData?.customFields?.length) {
      return [] as IKeyValue[];
    }
    const allFields = await this.getAllCustomFields();
    const returnFields: IKeyValue[] = [];
    this.LocalPatient.apData.customFields.map((field) => {
      const customField = allFields.find((f) => f.id === field.id);
      customField && returnFields.push({ [customField.name]: field.value });
    });
    return returnFields;
  }

  async createCustomField(name: string, dataType: string = "TEXT") {
    return await this.request.createCustomFieldIfNotExists(name, dataType);
  }
}
